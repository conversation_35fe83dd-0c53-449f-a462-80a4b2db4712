// Enhanced RTL Support for Project Filter
.rtl-filter {
  direction: rtl !important;
  text-align: right !important;

  .form-label {
    font-family: 'Noto <PERSON> Arabic', sans-serif !important;
    text-align: right !important;
    font-weight: 600 !important;
    color: #1e3a8a !important;
    margin-bottom: 0.5rem !important;
  }

  .form-control {
    direction: rtl !important;
    text-align: right !important;
    font-family: 'Hacen Liner Screen', sans-serif !important;
    border: 2px solid #e5e7eb !important;
    border-radius: 8px !important;
    padding: 0.5rem 0.75rem !important;

    &:focus {
      border-color: #1e3a8a !important;
      box-shadow: 0 0 0 0.2rem rgba(30, 58, 138, 0.25) !important;
    }

    &::placeholder {
      color: #9ca3af !important;
      font-family: 'Hacen Liner Screen', sans-serif !important;
      text-align: right !important;
    }
  }

  .btn {
    font-family: 'Hacen Liner Screen', sans-serif !important;
    font-weight: 600 !important;
    border-radius: 8px !important;
    padding: 0.5rem 1rem !important;
    transition: all 0.3s ease !important;

    &.btn-primary {
      background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%) !important;
      border: none !important;

      &:hover {
        background: linear-gradient(135deg, #1e40af 0%, #1d4ed8 100%) !important;
        transform: translateY(-1px) !important;
        box-shadow: 0 4px 12px rgba(30, 58, 138, 0.3) !important;
      }
    }
  }
}

// Enhanced Arabic support
:host-context(html[lang="ar"]) {
  .filter-dropdown {
    direction: rtl !important;
    text-align: right !important;
    padding: 1rem !important;
    border-radius: 12px !important;
    background: white !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
    border: 1px solid #e5e7eb !important;

    .mb-2 {
      margin-bottom: 1rem !important;
    }

    .form-label {
      font-family: 'Noto Kufi Arabic', sans-serif !important;
      font-size: 0.9rem !important;
      font-weight: 700 !important;
      color: #1e3a8a !important;
      text-align: right !important;
      margin-bottom: 0.5rem !important;
      display: block !important;
    }

    .form-control {
      direction: rtl !important;
      text-align: right !important;
      font-family: 'Hacen Liner Screen', sans-serif !important;
      border: 2px solid #e5e7eb !important;
      border-radius: 8px !important;
      padding: 0.6rem 0.75rem !important;
      font-size: 0.85rem !important;
      transition: all 0.3s ease !important;

      &:focus {
        border-color: #1e3a8a !important;
        box-shadow: 0 0 0 0.2rem rgba(30, 58, 138, 0.25) !important;
        outline: none !important;
      }

      &::placeholder {
        color: #9ca3af !important;
        font-family: 'Hacen Liner Screen', sans-serif !important;
        text-align: right !important;
        direction: rtl !important;
      }
    }

    .btn {
      font-family: 'Hacen Liner Screen', sans-serif !important;
      font-weight: 600 !important;
      border-radius: 8px !important;
      padding: 0.6rem 1rem !important;
      font-size: 0.85rem !important;
      transition: all 0.3s ease !important;

      &.btn-primary {
        background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%) !important;
        border: none !important;
        color: white !important;

        &:hover {
          background: linear-gradient(135deg, #1e40af 0%, #1d4ed8 100%) !important;
          transform: translateY(-1px) !important;
          box-shadow: 0 4px 12px rgba(30, 58, 138, 0.3) !important;
        }

        &:active {
          transform: translateY(0) !important;
        }
      }
    }
  }
}

// General improvements
.filter-dropdown {
  min-width: 225px !important;
  padding: 1rem;
  border-radius: 12px;
  background: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;

  .mb-2 {
    margin-bottom: 1rem;
  }

  .form-control-sm {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    border-radius: 6px;
  }

  .btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    font-weight: 600;
    border-radius: 6px;
  }
}

@media  (max-width: 576px) {
.filter-dropdown {
  min-width: 225px !important;
  padding: 1rem;
  border-radius: 12px;
  background: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}
  
}