// Enhanced RTL Support for Project Filter
.rtl-filter {
  direction: rtl !important;
  text-align: right !important;

  .form-label {
    font-family: 'Noto <PERSON> Arabic', sans-serif !important;
    text-align: right !important;
    font-weight: 600 !important;
    color: #1e3a8a !important;
    margin-bottom: 0.5rem !important;
  }

  .form-control {
    direction: rtl !important;
    text-align: right !important;
    font-family: 'Hacen Liner Screen', sans-serif !important;
    border: 2px solid #e5e7eb !important;
    border-radius: 8px !important;
    padding: 0.5rem 0.75rem !important;

    &:focus {
      border-color: #1e3a8a !important;
      box-shadow: 0 0 0 0.2rem rgba(30, 58, 138, 0.25) !important;
    }

    &::placeholder {
      color: #9ca3af !important;
      font-family: 'Hacen Liner Screen', sans-serif !important;
      text-align: right !important;
    }
  }

  .btn {
    font-family: 'Hacen Liner Screen', sans-serif !important;
    font-weight: 600 !important;
    border-radius: 8px !important;
    padding: 0.5rem 1rem !important;
    transition: all 0.3s ease !important;

    &.btn-primary {
      background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%) !important;
      border: none !important;

      &:hover {
        background: linear-gradient(135deg, #1e40af 0%, #1d4ed8 100%) !important;
        transform: translateY(-1px) !important;
        box-shadow: 0 4px 12px rgba(30, 58, 138, 0.3) !important;
      }
    }
  }
}

// Enhanced Arabic support
:host-context(html[lang="ar"]) {
  .filter-dropdown {
    direction: rtl !important;
    text-align: right !important;
    padding: 1rem !important;
    border-radius: 12px !important;
    background: white !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
    border: 1px solid #e5e7eb !important;

    .mb-2 {
      margin-bottom: 1rem !important;
    }

    .form-label {
      font-family: 'Noto Kufi Arabic', sans-serif !important;
      font-size: 0.9rem !important;
      font-weight: 700 !important;
      color: #1e3a8a !important;
      text-align: right !important;
      margin-bottom: 0.5rem !important;
      display: block !important;
    }

    .form-control {
      direction: rtl !important;
      text-align: right !important;
      font-family: 'Hacen Liner Screen', sans-serif !important;
      border: 2px solid #e5e7eb !important;
      border-radius: 8px !important;
      padding: 0.6rem 0.75rem !important;
      font-size: 0.85rem !important;
      transition: all 0.3s ease !important;

      &:focus {
        border-color: #1e3a8a !important;
        box-shadow: 0 0 0 0.2rem rgba(30, 58, 138, 0.25) !important;
        outline: none !important;
      }

      &::placeholder {
        color: #9ca3af !important;
        font-family: 'Hacen Liner Screen', sans-serif !important;
        text-align: right !important;
        direction: rtl !important;
      }
    }

    .btn {
      font-family: 'Hacen Liner Screen', sans-serif !important;
      font-weight: 600 !important;
      border-radius: 8px !important;
      padding: 0.6rem 1rem !important;
      font-size: 0.85rem !important;
      transition: all 0.3s ease !important;

      &.btn-primary {
        background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%) !important;
        border: none !important;
        color: white !important;

        &:hover {
          background: linear-gradient(135deg, #1e40af 0%, #1d4ed8 100%) !important;
          transform: translateY(-1px) !important;
          box-shadow: 0 4px 12px rgba(30, 58, 138, 0.3) !important;
        }

        &:active {
          transform: translateY(0) !important;
        }
      }
    }
  }
}

// General improvements
.filter-dropdown {
  min-width: 225px !important;
  padding: 1rem;
  border-radius: 12px;
  background: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;

  .mb-2 {
    margin-bottom: 1rem;
  }

  .form-control-sm {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    border-radius: 6px;
  }

  .btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    font-weight: 600;
    border-radius: 6px;
  }
}

// ===== RESPONSIVE DROPDOWN FOR MEDIUM SCREENS =====
@media (min-width: 425px) and (max-width: 700px) {
  .dropdown-menu.show.p-4.shadow {
    position: fixed !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    min-width: 300px !important;
    max-width: 85vw !important;
    width: auto !important;
    padding: 1.5rem !important;
    border-radius: 12px !important;
    background: white !important;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2) !important;
    border: 1px solid #e5e7eb !important;
    z-index: 1050 !important;

    // Add backdrop overlay
    &::before {
      content: '';
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.4);
      z-index: -1;
    }

    // Enhanced content styling for medium screens
    .filter-dropdown {
      min-width: 100% !important;
      padding: 0 !important;
      border: none !important;
      box-shadow: none !important;
      background: transparent !important;

      .mb-2 {
        margin-bottom: 1.2rem !important;
      }

      .form-label {
        font-size: 0.95rem !important;
        font-weight: 600 !important;
        margin-bottom: 0.6rem !important;
        color: #1e3a8a !important;
      }

      .form-control-sm {
        padding: 0.7rem 0.9rem !important;
        font-size: 0.9rem !important;
        border-radius: 8px !important;
        border: 2px solid #e5e7eb !important;
        transition: all 0.3s ease !important;

        &:focus {
          border-color: #1e3a8a !important;
          box-shadow: 0 0 0 0.2rem rgba(30, 58, 138, 0.25) !important;
          outline: none !important;
        }

        &::placeholder {
          color: #9ca3af !important;
        }
      }

      .btn-sm {
        padding: 0.7rem 1.5rem !important;
        font-size: 0.95rem !important;
        font-weight: 600 !important;
        border-radius: 8px !important;
        width: 100% !important;
        margin-top: 0.5rem !important;

        &.btn-primary {
          background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%) !important;
          border: none !important;

          &:hover {
            background: linear-gradient(135deg, #1e40af 0%, #1d4ed8 100%) !important;
            transform: translateY(-1px) !important;
            box-shadow: 0 4px 12px rgba(30, 58, 138, 0.3) !important;
          }
        }
      }
    }
  }
}

// ===== RTL SUPPORT FOR MEDIUM SCREENS =====
:host-context(html[lang="ar"]) {
  @media (min-width: 425px) and (max-width: 700px) {
    .dropdown-menu.show.p-4.shadow {
      .filter-dropdown {
        direction: rtl !important;
        text-align: right !important;

        .form-label {
          font-family: 'Noto Kufi Arabic', sans-serif !important;
          text-align: right !important;
          font-size: 1rem !important;
          font-weight: 700 !important;
        }

        .form-control-sm {
          font-family: 'Hacen Liner Screen', sans-serif !important;
          text-align: right !important;
          direction: rtl !important;
          font-size: 0.95rem !important;

          &::placeholder {
            font-family: 'Hacen Liner Screen', sans-serif !important;
            text-align: right !important;
            direction: rtl !important;
          }
        }

        .btn-sm {
          font-family: 'Hacen Liner Screen', sans-serif !important;
          font-size: 1rem !important;
          font-weight: 600 !important;
        }
      }
    }
  }
}
