.upload-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(46, 42, 126, 0.1);
  margin: 0 auto;
}

.cursor-pointer {
  cursor: pointer;
}

.border-primary {
  border-color: #2e2a7e !important;
}

.text-primary {
  color: #2e2a7e !important;
}

label.cursor-pointer {
  cursor: pointer;
  transition: all 0.2s ease;
}

// label.cursor-pointer:hover {
//   background-color: rgba(46, 42, 126, 0.05);
// }

.badge.bg-success {
  background-color: #50cd89 !important;
}

/* Developer Page RTL Support - Enhanced like All Brokers */

// RTL Table Support - Enhanced like All Brokers
.rtl-table {
  direction: rtl;

  .table {
    th, td {
      text-align: right;
    }

    .d-flex {
      &.flex-row-reverse {
        flex-direction: row-reverse !important;
      }
    }
  }
}

// Arabic Font Support - Enhanced like All Brokers
[style*="direction: rtl"] {
  .table {
    th, td {
      text-align: right !important;
      padding: 12px 16px;
    }

    .symbol {
      margin-left: 12px;
      margin-right: 0;
    }

    .badge {
      text-align: center;
    }
  }
}

/* Developer Page RTL Support - Simplified */

// RTL Support for Arabic Language - Component Specific
:host-context(html[lang="ar"]) {

  // Main container fixes - only for this component
  .d-flex.justify-content-between {
    flex-direction: row-reverse !important;
  }

  // Search input fixes
  .form-control {
    direction: rtl !important;
    text-align: right !important;

    &.form-control-flush {
      padding-right: 2.5rem !important;
      padding-left: 0.75rem !important;
    }
  }

  // Search icon position
  .position-relative {
    app-keenicon,
    .keenicon {
      right: 12px !important;
      left: auto !important;
    }
  }

  // Navigation tabs
  .nav-stretch {
    flex-direction: row-reverse !important;

    .nav-item {
      .nav-link {
        margin-left: 0.5rem !important;
        margin-right: 0 !important;
      }
    }
  }

  // Table alignment
  .table {
    th, td {
      text-align: right !important;

      &:last-child {
        text-align: left !important;
      }
    }
  }

  // Button spacing
  .btn {
    margin-left: 0.5rem !important;
    margin-right: 0 !important;
  }
}

// Enhanced styling for Arabic - Clean version
:host-context(html[lang="ar"]) {
  .btn {
    font-size: 1rem;
    font-weight: 600;
    font-family: ' Markazi Text ', sans-serif;
  }

  .nav-link.btn {
    font-size: 1rem;
    font-weight: 600;
    min-width: 140px;
    font-family: 'Markazi Text', sans-serif;
  }

  .modal-title {
    font-size: 1.3rem;
    font-weight: 700;
    font-family: 'Markazi Text', sans-serif;
  }

  h1, h4, h5 {
    font-family: 'Markazi Text', sans-serif;
  }

  .form-control {
    font-family: 'Markazi Text', sans-serif;
  }

  .table {
    th, td {
      font-family: 'Markazi Text', sans-serif;
    }
  }

  .badge {
    font-family: 'Markazi Text', sans-serif;
  }

  .text-muted {
    font-family: 'Markazi Text', sans-serif;
  }

  .dropdown-item {
    font-family: 'Markazi Text', sans-serif;
  }

  .modal-body {
    p, span {
      font-family: 'Markazi Text', sans-serif;
    }
  }

  // إصلاح ترتيب الصورة والنص في الجدول
  .table {
    .d-flex.align-items-center {
      flex-direction: row !important; // الصورة أولاً ثم النص

      .symbol {
        margin-left: 0 !important;
        margin-right: 0.75rem !important;
      }

      .d-flex.flex-column {
        text-align: right !important;
      }
    }
  }
}

// Responsive adjustments for Arabic
@media (max-width: 768px) {
  html[lang="ar"] {
    .nav-link.btn {
      min-width: 100px;
      font-size: 0.8rem;
    }

    .btn {
      font-size: 0.8rem;
      padding: 0.375rem 0.75rem;
    }
  }
}

// إصلاحات إضافية للتصميم العربي
.developers-page {

   .position-relative {
    .form-control {
      border: 1px solid #e1e5e9 !important;
      box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;

      &:focus {
        border-color: #80bdff !important;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
      }
    }

    app-keenicon {
      z-index: 10;
    }
  }

  .nav-link.btn {
    border: 1px solid transparent;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    }
  }

  .d-flex.justify-content-between {
    gap: 1rem;

    @media (max-width: 768px) {
      flex-direction: column;
      align-items: center;
      text-align: center;
    }
  }

   &.rtl-layout {
    .table {
       th, td {
        text-align: right !important;

        &:first-child {
           text-align: center !important;
        }

        &:last-child {
           text-align: left !important;
        }
      }

       .d-flex.align-items-center {
        .symbol {
          margin-left: 0 !important;
          margin-right: 0.75rem !important;
        }

        .d-flex.flex-column {
          text-align: right !important;
        }
      }

       .badge {
        text-align: center !important;

        &.badge-light-primary,
        &.badge-light-info,
        &.badge-light-success {
          font-family: 'Hacen Liner Screen, sans-serif' !important;
        }
      }

       .btn {
        &.btn-sm {
          font-family: 'Hacen Liner Screen, sans-serif' !important;
        }
      }
    }
  }
}
:host-context(html[lang="ar"]) {
  .table {

    th {
      text-align: center !important;
      vertical-align: middle !important;
      padding: 1rem 0.75rem !important;

      &:first-child {
        text-align: right !important;
        padding-right: 1rem !important;
      }

      &:last-child {
        text-align: left !important;
        padding-left: 1rem !important;
      }
    }


    td {
      text-align: center !important;
      vertical-align: middle !important;
      padding: 1rem 0.75rem !important;

      &:first-child {
        text-align: right !important;
        padding-right: 1rem !important;
      }

      &:last-child {
        text-align: left !important;
        padding-right: 4rem !important;
      }
    }

    .d-flex.align-items-center {
      justify-content: start !important;

      .symbol {
        margin-right: 0.75rem !important;
        margin-left: 0 !important;
      }

      .d-flex.flex-column {
        text-align: right !important;
      }
    }

     .dropdown {
      .dropdown-menu {
        right: 0 !important;
        left: auto !important;

        .dropdown-item {
          text-align: right !important;
          flex-direction: row-reverse !important;

          i {
            margin-right: 0 !important;
            margin-left: 0.5rem !important;
          }
        }
      }
    }
  }
}

// ===== OVERRIDE GLOBAL STYLES =====

 .developers-page {
  .nav-stretch {
    .nav-item {
      .nav-link {
        margin-top: 0 !important;

        &.fw-bolder {
          margin-top: 0 !important;
        }
      }
    }
  }
}

 html[dir=rtl] .developers-page .nav-stretch .nav-item .nav-link,
html[lang=ar] .developers-page .nav-stretch .nav-item .nav-link {
  margin-top: 0 !important;

  &.fw-bolder {
    margin-top: 0 !important;
  }
}




// Small screens (426px - 576px)
@media (min-width: 426px) and (max-width: 576px) {
  .d-flex.h-auto.col-6.mt-4.justify-content-center.justify-content-md-start {
    .nav-stretch {
      .nav-item {
        .nav-link.btn {
          font-size: 0.75rem !important;
          padding: 0.5rem 0.8rem !important;
          margin: 0 3px !important;
        }
      }
    }
  }
}

// @media (max-width: 320px) {
//   .d-flex.h-auto.col-6.mt-4.justify-content-center.justify-content-md-start {
//     .nav-stretch {
//       margin-left: 0% !important;
//     }
//   }
// }

// ===== RESPONSIVE DESIGN ENHANCEMENTS =====

// Mobile-first responsive design
.developers-page {
  // padding: 0 15px;

  @media (min-width: 768px) {
    padding: 0 30px;
  }

  @media (min-width: 1200px) {
    padding: 0 50px;
  }
}

// ===== ENHANCED RESPONSIVE FOR SMALL SCREENS (320px - 700px) =====

// Very small screens (320px - 424px)
@media (max-width: 424px) {
  .d-flex.h-auto.col-6.mt-4.justify-content-center.justify-content-md-start {
    width: 100% !important;
    max-width: 100% !important;
    flex: 1 1 100% !important;
    justify-content: center !important;
    padding: 0 8px !important;
    margin-top: 0.5rem !important;

    .nav-stretch {
      justify-content: center !important;
      flex-wrap: nowrap !important;
      gap: 3px !important;
      width: 100% !important;

      .nav-item {
        flex: 1;
        margin-bottom: 0 !important;

        .nav-link.btn {
          width: 100% !important;
          margin: 0 !important;
          font-size: 0.55rem !important;
          padding: 0.3rem 0.1rem !important;
          min-width: auto !important;
          text-align: center !important;
          white-space: nowrap;
          border-radius: 0.25rem !important;
          line-height: 1.1;
          font-weight: 500 !important;
          height: 32px !important;
          display: flex !important;
          align-items: center !important;
          justify-content: center !important;

          // Remove all margins
          &.ms-1, &.ms-2, &.ms-3, &.ms-4, &.ms-5 {
            margin-left: 0 !important;
          }

          &.me-1, &.me-2, &.me-3, &.me-4, &.me-5 {
            margin-right: 0 !important;
          }
        }
      }
    }
  }
}

// Small screens (425px - 700px)
@media (min-width: 425px) and (max-width: 700px) {
  .d-flex.h-auto.col-6.mt-4.justify-content-center.justify-content-md-start {
    width: 100% !important;
    max-width: 100% !important;
    flex: 1 1 100% !important;
    justify-content: center !important;
    padding: 0 10px !important;
    margin-top: 0.75rem !important;

    .nav-stretch {
      justify-content: center !important;
      flex-wrap: nowrap !important;
      gap: 5px !important;
      width: 100% !important;

      .nav-item {
        flex: 1;
        margin-bottom: 0 !important;

        .nav-link.btn {
          width: 100% !important;
          margin: 0 !important;
          font-size: 0.65rem !important;
          padding: 0.4rem 0.2rem !important;
          min-width: auto !important;
          text-align: center !important;
          white-space: nowrap;
          border-radius: 0.3rem !important;
          line-height: 1.2;
          font-weight: 500 !important;
          height: 36px !important;
          display: flex !important;
          align-items: center !important;
          justify-content: center !important;

          // Remove all margins
          &.ms-1, &.ms-2, &.ms-3, &.ms-4, &.ms-5 {
            margin-left: 0 !important;
          }

          &.me-1, &.me-2, &.me-3, &.me-4, &.me-5 {
            margin-right: 0 !important;
          }
        }
      }
    }
  }
}

// Header section responsive
.d-flex.justify-content-between.align-items-start.flex-wrap {
  gap: 1rem;

  @media (max-width: 767.98px) {
    flex-direction: column !important;
    align-items: center !important;
    text-align: center;

    .d-flex.my-4 {
      margin: 0.5rem 0 !important;
      width: 100%;
      justify-content: center;

      h1 {
        font-size: 1.5rem !important;
        margin: 0 !important;
      }

      // Search form mobile
      form {
        width: 100% !important;
        max-width: 300px;

        .form-control {
          font-size: 0.9rem;
          padding: 0.75rem 1rem;
        }
      }

      // Buttons mobile
      .btn {
        font-size: 0.8rem !important;
        padding: 0.5rem 1rem !important;

        i {
          font-size: 0.8rem;
        }
      }
    }
  }

  @media (min-width: 768px) and (max-width: 991.98px) {
    .d-flex.my-4 {
      h1 {
        font-size: 1.75rem;
      }

      form {
        width: 250px;
      }

      .btn {
        font-size: 0.9rem;
        padding: 0.6rem 1.2rem;
      }
    }
  }
}

// Navigation tabs responsive - Enhanced sizing
.nav-stretch {
  .nav-item {
    .nav-link.btn {
      font-size: 1rem !important;
      padding: 0.75rem 1.5rem !important;
      min-width: 140px !important;
      font-weight: 600 !important;
      border-radius: 0.5rem !important;
      transition: all 0.3s ease !important;
      margin-top: 0 !important; // Override global fw-bolder margin-top

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
      }

      &.fw-bolder {
        margin-top: 0 !important; // Specific override for fw-bolder class
      }
    }
  }

  @media (max-width: 575.98px) {
    flex-wrap: wrap !important;
    justify-content: center;

    .nav-item {
      margin-bottom: 0.75rem;

      .nav-link.btn {
        font-size: 0.9rem !important;
        padding: 0.6rem 1.2rem !important;
        min-width: 120px !important;
        white-space: nowrap;
      }
    }
  }

  @media (min-width: 576px) and (max-width: 767.98px) {
    .nav-item {
      .nav-link.btn {
        font-size: 0.95rem !important;
        padding: 0.7rem 1.3rem !important;
        min-width: 130px !important;
      }
    }
  }

  @media (min-width: 768px) {
    .nav-item {
      .nav-link.btn {
        font-size: 1.1rem !important;
        padding: 0.8rem 1.6rem !important;
        min-width: 150px !important;
      }
    }
  }
}

// Table responsive enhancements - Comprehensive
.table-responsive {

  // Enhanced developers table specific styling
  .developers-table {

    // Mobile First (< 576px)
    @media (max-width: 575.98px) {
      font-size: 0.75rem;

      th, td {
        padding: 0.4rem 0.2rem !important;
        vertical-align: middle;
        border: none !important;
      }

      thead th {
        font-size: 0.7rem !important;
        padding: 0.5rem 0.2rem !important;

        &:first-child {
          width: 40px !important;
        }

        &:nth-child(2) {
          min-width: 140px !important;
        }

        &:nth-child(3) {
          min-width: 120px !important;
        }

        &:last-child {
          width: 60px !important;
        }
      }

      tbody {
        tr {
          border-bottom: 1px solid #e1e5e9 !important;
          margin-bottom: 0.5rem;

          td {
            &:first-child {
              padding: 0.3rem !important;
            }

            // Developer name column
            &:nth-child(2) {
              .d-flex.align-items-center {
                .symbol {
                  width: 28px !important;
                  height: 28px !important;
                  margin-right: 0.4rem !important;

                  img {
                    width: 28px !important;
                    height: 28px !important;
                  }

                  .symbol-label {
                    font-size: 0.6rem !important;
                  }
                }

                .d-flex.flex-column {
                  .text-gray-900 {
                    font-size: 0.75rem !important;
                    line-height: 1.2;
                  }

                  .text-muted {
                    font-size: 0.65rem !important;
                    line-height: 1.1;
                  }
                }
              }
            }

            // Email column with mobile status
            &:nth-child(3) {
              .d-flex.flex-column {
                .text-gray-800 {
                  font-size: 0.65rem !important;
                  line-height: 1.2;
                }

                .text-muted {
                  font-size: 0.6rem !important;
                  line-height: 1.1;
                }

                // Mobile status styling
                .d-md-none {
                  .btn {
                    font-size: 0.6rem !important;
                    padding: 0.2rem 0.4rem !important;

                    &.btn-xs {
                      font-size: 0.55rem !important;
                      padding: 0.15rem 0.3rem !important;
                    }
                  }

                  .badge {
                    font-size: 0.55rem !important;
                    padding: 0.15rem 0.3rem !important;

                    &.fs-8 {
                      font-size: 0.5rem !important;
                    }
                  }
                }
              }
            }

            // Actions column
            &:last-child {
              .btn {
                width: 28px !important;
                height: 28px !important;
                padding: 0 !important;

                i {
                  font-size: 0.7rem !important;
                }
              }
            }
          }
        }
      }
    }

    // Small tablets (576px - 767px)
    @media (min-width: 576px) and (max-width: 767.98px) {
      font-size: 0.85rem;

      th, td {
        padding: 0.6rem 0.4rem !important;
      }

      thead th {
        font-size: 0.8rem !important;
      }

      .d-flex.align-items-center {
        .symbol {
          width: 32px !important;
          height: 32px !important;

          img {
            width: 32px !important;
            height: 32px !important;
          }

          .symbol-label {
            font-size: 0.7rem !important;
          }
        }

        .d-flex.flex-column {
          .text-gray-900 {
            font-size: 0.8rem !important;
          }

          .text-muted {
            font-size: 0.7rem !important;
          }
        }
      }

      .btn {
        font-size: 0.75rem !important;
        padding: 0.3rem 0.6rem !important;

        &.btn-sm {
          font-size: 0.7rem !important;
          padding: 0.25rem 0.5rem !important;
        }
      }

      .badge {
        font-size: 0.7rem !important;
        padding: 0.3rem 0.6rem !important;
      }
    }

    // Medium tablets (768px - 991px)
    @media (min-width: 768px) and (max-width: 991.98px) {
      font-size: 0.9rem;

      th, td {
        padding: 0.8rem 0.6rem !important;
      }

      .d-flex.align-items-center {
        .symbol {
          width: 36px !important;
          height: 36px !important;

          img {
            width: 36px !important;
            height: 36px !important;
          }
        }
      }

      .btn {
        font-size: 0.85rem !important;
        padding: 0.4rem 0.8rem !important;
      }
    }

    // Large screens (992px - 1199px)
    @media (min-width: 992px) and (max-width: 1199.98px) {
      th, td {
        padding: 1rem 0.8rem !important;
      }
    }

    // Extra large screens (1200px+)
    @media (min-width: 1200px) {
      th, td {
        padding: 1.2rem 1rem !important;
      }
    }
  }
}

// Modal responsive
.modal-dialog {
  @media (max-width: 575.98px) {
    margin: 0.5rem;
    max-width: calc(100% - 1rem);

    .modal-content {
      .modal-header {
        padding: 1rem;

        .modal-title {
          font-size: 1.1rem !important;
        }
      }

      .modal-body {
        padding: 1rem;

        .container {
          padding: 1rem !important;

          h5 {
            font-size: 1rem !important;
          }

          p {
            font-size: 0.8rem !important;
          }

          .border.rounded {
            padding: 1rem !important;

            .upload-icon {
              width: 30px !important;
              height: 30px !important;

              i {
                font-size: 16px !important;
              }
            }

            p {
              font-size: 0.8rem !important;

              &.small {
                font-size: 0.7rem !important;
              }
            }
          }
        }
      }

      .modal-footer {
        padding: 1rem;

        .btn {
          font-size: 0.9rem;
          padding: 0.5rem 1rem;
        }
      }
    }
  }

  @media (min-width: 576px) and (max-width: 767.98px) {
    .modal-content {
      .modal-body {
        .container {
          .border.rounded {
            padding: 1.5rem !important;
          }
        }
      }
    }
  }
}

// Pagination responsive
.d-flex.justify-content-center {
  @media (max-width: 575.98px) {
    app-pagination {
      ::ng-deep {
        .pagination {
          .page-item {
            .page-link {
              font-size: 0.8rem;
              padding: 0.375rem 0.5rem;
            }
          }
        }
      }
    }
  }
}

// RTL responsive adjustments with enhanced button sizing
:host-context(html[lang="ar"]) {

  // Very small screens for Arabic (320px - 424px)
  @media (max-width: 424px) {
    .d-flex.h-auto.col-6.mt-4.justify-content-center.justify-content-md-start {
      .nav-stretch {
        .nav-item {
          .nav-link.btn {
            font-family: 'Markazi Text', sans-serif !important;
            font-size: 0.6rem !important;
            padding: 0.3rem 0.1rem !important;
            font-weight: 500 !important;
            line-height: 1.1 !important;
            height: 32px !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;

            // Override margins for Arabic
            &.ms-1, &.ms-2, &.ms-3, &.ms-4, &.ms-5 {
              margin-left: 0 !important;
            }

            &.me-1, &.me-2, &.me-3, &.me-4, &.me-5 {
              margin-right: 0 !important;
            }
          }
        }
      }
    }
  }

  // Small screens for Arabic (425px - 700px)
  @media (min-width: 425px) and (max-width: 700px) {
    .d-flex.h-auto.col-6.mt-4.justify-content-center.justify-content-md-start {
      .nav-stretch {
        .nav-item {
          .nav-link.btn {
            font-family: 'Markazi Text', sans-serif !important;
            font-size: 0.7rem !important;
            padding: 0.4rem 0.2rem !important;
            font-weight: 500 !important;
            line-height: 1.2 !important;
            height: 36px !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;

            // Override margins for Arabic
            &.ms-1, &.ms-2, &.ms-3, &.ms-4, &.ms-5 {
              margin-left: 0 !important;
            }

            &.me-1, &.me-2, &.me-3, &.me-4, &.me-5 {
              margin-right: 0 !important;
            }
          }
        }
      }
    }
  }

  // Enhanced navigation tabs for Arabic on larger screens
  .nav-stretch {
    .nav-item {
      .nav-link.btn {
        font-family: 'Markazi Text', sans-serif !important;
        font-size: 1.1rem !important;
        padding: 0.8rem 1.8rem !important;
        min-width: 160px !important;
        font-weight: 700 !important;
        margin-top: 0 !important; // Override global fw-bolder margin-top for Arabic

        &.fw-bolder {
          margin-top: 0 !important; // Specific override for fw-bolder class in Arabic
        }

        @media (max-width: 575.98px) {
          font-size: 1rem !important;
          padding: 0.7rem 1.4rem !important;
          min-width: 140px !important;
        }

        @media (min-width: 576px) and (max-width: 767.98px) {
          font-size: 1.05rem !important;
          padding: 0.75rem 1.6rem !important;
          min-width: 150px !important;
        }

        @media (min-width: 768px) {
          font-size: 1.2rem !important;
          padding: 0.9rem 2rem !important;
          min-width: 170px !important;
        }
      }
    }
  }

  @media (max-width: 767.98px) {
    .d-flex.justify-content-between.align-items-start.flex-wrap {
      .d-flex.my-4 {
        .btn {
          font-family: 'Markazi Text', sans-serif !important;
        }
      }
    }

    .table {
      th, td {
        font-family: 'Markazi Text', sans-serif !important;
      }
    }
  }
}
